<?xml version="1.0" encoding="UTF-8"?>
<architecture>
  <layers>
    <layer name="api" level="1">
      <component name="main.py" desc="FastAPI应用入口"/>
      <component name="routers/agent_router.py" desc="Agent API路由"/>
      <component name="routers/team_router.py" desc="Team API路由"/>
    </layer>
    
    <layer name="service" level="2">
      <component name="services/agent_service.py" desc="Agent业务逻辑"/>
      <component name="services/team_service.py" desc="Team业务逻辑"/>
    </layer>
    
    <layer name="manager" level="3">
      <component name="managers/agent_manager.py" desc="Agent动态管理"/>
      <component name="managers/team_manager.py" desc="Team动态管理"/>
      <component name="managers/model_manager.py" desc="模型配置管理"/>
      <component name="managers/tool_manager.py" desc="工具管理"/>
      <component name="managers/prompt_manager.py" desc="提示词管理"/>
    </layer>
    
    <layer name="definition" level="4">
      <component name="agents/" desc="Agent代码定义目录"/>
      <component name="teams/" desc="Team代码定义目录"/>
    </layer>
    
    <layer name="config" level="5">
      <component name="config/model_config.yaml" desc="模型配置"/>
      <component name="config/tools_config.yaml" desc="工具配置"/>
      <component name="prompts/" desc="提示词MD文件"/>
    </layer>
    
    <layer name="infrastructure" level="6">
      <component name="infrastructure/logger.py" desc="日志系统"/>
      <component name="infrastructure/memory/" desc="Memory系统"/>
      <component name="infrastructure/rag/" desc="RAG系统"/>
      <component name="infrastructure/tools/" desc="工具系统"/>
      <component name="infrastructure/exceptions.py" desc="异常处理"/>
    </layer>
  </layers>
  
  <directory_structure>
    <root name="autogen_framework">
      <file name="main.py"/>
      <dir name="routers">
        <file name="agent_router.py"/>
        <file name="team_router.py"/>
      </dir>
      <dir name="services">
        <file name="agent_service.py"/>
        <file name="team_service.py"/>
      </dir>
      <dir name="managers">
        <file name="agent_manager.py"/>
        <file name="team_manager.py"/>
        <file name="model_manager.py"/>
        <file name="tool_manager.py"/>
        <file name="prompt_manager.py"/>
      </dir>
      <dir name="agents">
        <file name="code_reviewer_agent.py"/>
        <file name="developer_agent.py"/>
        <file name="qa_tester_agent.py"/>
      </dir>
      <dir name="teams">
        <file name="development_team.py"/>
        <file name="review_team.py"/>
      </dir>
      <dir name="prompts">
        <dir name="agents"/>
        <dir name="teams"/>
        <dir name="common"/>
      </dir>
      <dir name="infrastructure">
        <file name="logger.py"/>
        <dir name="memory">
          <file name="memory_manager.py"/>
          <dir name="backends"/>
          <dir name="types"/>
        </dir>
        <dir name="rag">
          <file name="rag_manager.py"/>
          <dir name="vector_stores"/>
          <dir name="embeddings"/>
        </dir>
        <dir name="tools">
          <file name="http_tools.py"/>
          <file name="mcp_tools.py"/>
          <file name="tool_registry.py"/>
        </dir>
        <file name="exceptions.py"/>
      </dir>
      <dir name="config">
        <file name="model_config.yaml"/>
        <file name="tools_config.yaml"/>
        <file name="app_config.yaml"/>
      </dir>
      <dir name="tests"/>
    </root>
  </directory_structure>
</architecture>
