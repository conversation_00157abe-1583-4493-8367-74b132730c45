<?xml version="1.0" encoding="UTF-8"?>
<project>
  <name>AutoGen Multi-Agent Framework</name>
  <description>基于AutoGen和FastAPI的企业级多智能体系统</description>
  
  <requirements>
    <api_layer>
      <endpoint>/autogen/run/agent</endpoint>
      <endpoint>/autogen/run/team</endpoint>
      <parameters>
        <param name="name" type="string" desc="agent或team别名"/>
        <param name="message" type="string" desc="用户消息"/>
        <param name="model" type="string" desc="大模型别名"/>
      </parameters>
    </api_layer>
    
    <service_layer>
      <module name="agent_service" method="run"/>
      <module name="team_service" method="run"/>
    </service_layer>
    
    <manager_layer>
      <module name="agent_manager" desc="动态加载agent，支持team中引用"/>
      <module name="team_manager" desc="动态加载team"/>
      <module name="model_manager" desc="根据别名动态加载模型配置"/>
      <module name="tool_manager" desc="支持HTTP和MCP工具"/>
      <module name="prompt_manager" desc="管理MD文件提示词"/>
    </manager_layer>
    
    <config_layer>
      <file name="model_config.yaml" desc="多模型配置管理"/>
      <directory name="prompts/" desc="MD文件提示词目录"/>
      <file name="tools_config.yaml" desc="HTTP和MCP工具配置"/>
    </config_layer>
    
    <infrastructure_layer>
      <module name="logger" desc="标准化日志管理"/>
      <module name="memory" desc="Memory管理和接入"/>
      <module name="rag" desc="RAG集成"/>
      <module name="exceptions" desc="异常处理"/>
    </infrastructure_layer>
  </requirements>
  
  <key_principles>
    <principle>Agent和Team通过代码定义，不使用配置文件</principle>
    <principle>每个提示词一个MD文件</principle>
    <principle>支持Memory和RAG基础设施</principle>
    <principle>工具支持HTTP和MCP接入</principle>
    <principle>全异步设计</principle>
    <principle>动态加载和热重载</principle>
  </key_principles>
</project>
