<?xml version="1.0" encoding="UTF-8"?>
<code_templates>
  <agent_template>
    <file_path>agents/example_agent.py</file_path>
    <template>
from autogen import ConversableAgent
from managers.prompt_manager import PromptManager
from managers.model_manager import <PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional

def create_example_agent(
    model_alias: str,
    name: Optional[str] = None,
    **kwargs
) -> ConversableAgent:
    """创建示例Agent
    
    Args:
        model_alias: 模型别名
        name: Agent名称，默认使用函数名推导
        **kwargs: 其他配置参数
    
    Returns:
        ConversableAgent实例
    """
    # 获取提示词
    prompt = PromptManager.get_prompt("agents/example_agent", **kwargs)
    
    # 获取模型配置
    model_config = ModelManager.get_model_config(model_alias)
    
    # 创建Agent
    agent = ConversableAgent(
        name=name or "example_agent",
        system_message=prompt,
        llm_config=model_config,
        **kwargs
    )
    
    return agent
    </template>
  </agent_template>
  
  <team_template>
    <file_path>teams/example_team.py</file_path>
    <template>
from autogen import GroupChat, GroupChatManager
from managers.agent_manager import Agent<PERSON>anager
from managers.model_manager import ModelManager
from typing import List, Dict, Any

def create_example_team(
    model_alias: str,
    agent_manager: AgentManager,
    **kwargs
) -> GroupChatManager:
    """创建示例Team
    
    Args:
        model_alias: 模型别名
        agent_manager: Agent管理器实例
        **kwargs: 其他配置参数
    
    Returns:
        GroupChatManager实例
    """
    # 创建team中的agents
    agents = [
        agent_manager.create_agent("agent1", model_alias),
        agent_manager.create_agent("agent2", model_alias),
    ]
    
    # 创建群聊
    group_chat = GroupChat(
        agents=agents,
        messages=[],
        max_round=kwargs.get("max_round", 10),
        speaker_selection_method=kwargs.get("speaker_selection", "auto")
    )
    
    # 创建管理器
    manager = GroupChatManager(
        groupchat=group_chat,
        llm_config=ModelManager.get_model_config(model_alias)
    )
    
    return manager
    </template>
  </team_template>
  
  <manager_template>
    <file_path>managers/example_manager.py</file_path>
    <template>
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)

class BaseManager(ABC):
    """管理器基类"""
    
    def __init__(self):
        self._registry: Dict[str, Any] = {}
        self._cache: Dict[str, Any] = {}
    
    @abstractmethod
    def register(self, name: str, item: Any) -> None:
        """注册项目"""
        pass
    
    @abstractmethod
    def get(self, name: str, **kwargs) -> Any:
        """获取项目"""
        pass
    
    def list_available(self) -> List[str]:
        """列出所有可用项目"""
        return list(self._registry.keys())
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()

class ExampleManager(BaseManager):
    """示例管理器"""
    
    def register(self, name: str, factory_func: callable) -> None:
        """注册工厂函数"""
        self._registry[name] = factory_func
        logger.info(f"Registered {name}")
    
    def get(self, name: str, **kwargs) -> Any:
        """获取实例"""
        if name not in self._registry:
            raise ValueError(f"Unknown item: {name}")
        
        # 检查缓存
        cache_key = f"{name}_{hash(str(sorted(kwargs.items())))}"
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # 创建新实例
        factory_func = self._registry[name]
        instance = factory_func(**kwargs)
        
        # 缓存实例
        self._cache[cache_key] = instance
        
        return instance
    </template>
  </manager_template>
  
  <service_template>
    <file_path>services/example_service.py</file_path>
    <template>
from typing import Dict, Any
from managers.agent_manager import AgentManager
from managers.team_manager import TeamManager
from infrastructure.logger import get_logger
from infrastructure.exceptions import ServiceError
import asyncio

logger = get_logger(__name__)

class ExampleService:
    """示例服务"""
    
    def __init__(self, agent_manager: AgentManager, team_manager: TeamManager):
        self.agent_manager = agent_manager
        self.team_manager = team_manager
    
    async def run(self, name: str, message: str, model: str, **kwargs) -> Dict[str, Any]:
        """执行服务
        
        Args:
            name: 项目名称
            message: 用户消息
            model: 模型别名
            **kwargs: 其他参数
        
        Returns:
            执行结果
        """
        try:
            logger.info(f"Starting service run: {name}, model: {model}")
            
            # 获取实例
            instance = self._get_instance(name, model, **kwargs)
            
            # 执行任务
            result = await self._execute(instance, message, **kwargs)
            
            logger.info(f"Service run completed: {name}")
            return {
                "status": "success",
                "result": result,
                "metadata": {
                    "name": name,
                    "model": model,
                    "message_length": len(message)
                }
            }
            
        except Exception as e:
            logger.error(f"Service run failed: {name}, error: {str(e)}")
            raise ServiceError(f"Service execution failed: {str(e)}")
    
    def _get_instance(self, name: str, model: str, **kwargs):
        """获取实例（子类实现）"""
        raise NotImplementedError
    
    async def _execute(self, instance, message: str, **kwargs):
        """执行任务（子类实现）"""
        raise NotImplementedError
    </template>
  </service_template>
  
  <api_template>
    <file_path>routers/example_router.py</file_path>
    <template>
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional
from services.example_service import ExampleService
from infrastructure.dependencies import get_example_service
from infrastructure.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/example", tags=["example"])

class RunRequest(BaseModel):
    name: str
    message: str
    model: str
    options: Optional[Dict[str, Any]] = None

class RunResponse(BaseModel):
    status: str
    result: Any
    metadata: Dict[str, Any]

@router.post("/run", response_model=RunResponse)
async def run_example(
    request: RunRequest,
    service: ExampleService = Depends(get_example_service)
):
    """执行示例任务"""
    try:
        result = await service.run(
            name=request.name,
            message=request.message,
            model=request.model,
            **(request.options or {})
        )
        return RunResponse(**result)
    
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list")
async def list_examples(
    service: ExampleService = Depends(get_example_service)
):
    """列出所有可用示例"""
    return {"examples": service.list_available()}
    </template>
  </api_template>
</code_templates>
