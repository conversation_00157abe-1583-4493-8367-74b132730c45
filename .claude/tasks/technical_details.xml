<?xml version="1.0" encoding="UTF-8"?>
<technical_details>
  <agent_management>
    <concept>Agent通过代码定义，使用工厂模式动态创建</concept>
    <implementation>
      <step>创建agent定义文件，每个agent一个py文件</step>
      <step>agent_manager维护工厂函数注册表</step>
      <step>通过别名动态调用工厂函数创建agent实例</step>
      <step>支持参数传递和配置覆盖</step>
    </implementation>
    <example_pattern>
      <code_structure>
        <file name="agents/code_reviewer_agent.py">
          <function name="create_code_reviewer_agent">
            <param name="model_alias" type="str"/>
            <param name="kwargs" type="dict"/>
            <return type="ConversableAgent"/>
          </function>
        </file>
      </code_structure>
      <registration>
        <method>agent_manager.register_agent_factory("code_reviewer", create_code_reviewer_agent)</method>
      </registration>
      <usage>
        <method>agent_manager.create_agent("code_reviewer", "Qwen2.5-32B-Instruct")</method>
      </usage>
    </example_pattern>
  </agent_management>
  
  <team_management>
    <concept>Team通过代码定义，引用已注册的agent</concept>
    <implementation>
      <step>创建team定义文件，每个team一个py文件</step>
      <step>team_manager维护team工厂函数注册表</step>
      <step>team通过agent_manager获取agent实例</step>
      <step>支持复杂的team工作流定义</step>
    </implementation>
    <example_pattern>
      <code_structure>
        <file name="teams/development_team.py">
          <function name="create_development_team">
            <param name="model_alias" type="str"/>
            <param name="agent_manager" type="AgentManager"/>
            <return type="Team"/>
          </function>
        </file>
      </code_structure>
    </example_pattern>
  </team_management>
  
  <prompt_management>
    <concept>每个提示词一个MD文件，支持模板变量</concept>
    <implementation>
      <step>prompts目录按类型组织（agents/teams/common）</step>
      <step>使用Jinja2模板引擎处理变量替换</step>
      <step>支持prompt继承和组合</step>
      <step>提供热重载机制</step>
    </implementation>
    <file_format>
      <structure>
        <section name="metadata">YAML front matter</section>
        <section name="content">Markdown内容</section>
        <section name="variables">模板变量定义</section>
      </structure>
    </file_format>
  </prompt_management>
  
  <model_management>
    <concept>通过别名管理多模型配置，支持动态切换</concept>
    <implementation>
      <step>model_config.yaml定义所有模型配置</step>
      <step>model_manager提供统一的模型获取接口</step>
      <step>支持模型能力检查和验证</step>
      <step>缓存模型实例提高性能</step>
    </implementation>
    <config_structure>
      <field name="provider">模型提供商类路径</field>
      <field name="config">模型具体配置</field>
      <field name="model_capabilities">模型能力描述</field>
    </config_structure>
  </model_management>
  
  <tool_management>
    <concept>支持HTTP和MCP两种工具类型</concept>
    <http_tools>
      <implementation>
        <step>tools_config.yaml定义HTTP工具配置</step>
        <step>支持多种认证方式</step>
        <step>自动重试和错误处理</step>
        <step>响应格式标准化</step>
      </implementation>
    </http_tools>
    <mcp_tools>
      <implementation>
        <step>支持MCP服务器连接管理</step>
        <step>自动工具发现和注册</step>
        <step>安全沙箱执行</step>
        <step>工具权限控制</step>
      </implementation>
    </mcp_tools>
  </tool_management>
  
  <memory_integration>
    <concept>基于AutoGen Memory接口的多后端支持</concept>
    <implementation>
      <step>memory_manager提供统一接口</step>
      <step>支持InMemory、Redis、Database后端</step>
      <step>分类管理：对话记忆、知识记忆、用户记忆</step>
      <step>提供记忆清理和归档机制</step>
    </implementation>
  </memory_integration>
  
  <rag_integration>
    <concept>与AutoGen RetrieveUserProxyAgent集成</concept>
    <implementation>
      <step>rag_manager管理向量数据库</step>
      <step>支持多种向量存储后端</step>
      <step>文档预处理和分块策略</step>
      <step>检索结果排序和过滤</step>
    </implementation>
  </rag_integration>

  <logging_system>
    <concept>使用AutoGen内置日志系统，支持trace和structured logging</concept>
    <implementation>
      <step>配置AutoGen的TRACE_LOGGER_NAME用于调试日志</step>
      <step>配置AutoGen的EVENT_LOGGER_NAME用于结构化事件日志</step>
      <step>集成到FastAPI应用的日志配置中</step>
      <step>提供日志级别和输出格式的配置管理</step>
    </implementation>
    <logger_types>
      <trace_logger>
        <name>autogen_core.TRACE_LOGGER_NAME</name>
        <purpose>人类可读的调试信息</purpose>
        <level>DEBUG</level>
      </trace_logger>
      <event_logger>
        <name>autogen_core.EVENT_LOGGER_NAME</name>
        <purpose>结构化事件日志，可被其他系统消费</purpose>
        <level>INFO</level>
      </event_logger>
    </logger_types>
    <usage_pattern>
      <code_example>
        import logging
        from autogen_core import TRACE_LOGGER_NAME, EVENT_LOGGER_NAME

        # 子模块日志
        trace_logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.my_module")
        event_logger = logging.getLogger(f"{EVENT_LOGGER_NAME}.my_module")
      </code_example>
    </usage_pattern>
  </logging_system>
</technical_details>
