# AutoGen多Agent框架项目 - 唤醒指令

## 立即执行步骤

### 1. 项目状态恢复
```bash
# 首先查看项目记忆文件
view .claude/tasks/task_tracker.xml
```
- 查看 `<current_phase>` 和 `<current_task>` 了解当前进度
- 查看 `<next_actions>` 了解下一步要做什么
- 查看 `<in_progress_tasks>` 了解正在进行的任务

### 2. 技术细节回顾
```bash
# 查看技术实现细节
view .claude/tasks/technical_details.xml
```
- 重点关注当前任务相关的技术实现方案
- 查看 `<implementation_notes>` 中的关键实现要点

### 3. 架构设计确认
```bash
# 确认项目架构
view .claude/tasks/architecture_design.xml
```
- 查看 `<directory_structure>` 了解项目结构
- 确认当前需要创建或修改的文件位置

## 工作流程

### A. 开始新任务前
1. **检查依赖**：在task_tracker.xml中查看当前任务的依赖是否已完成
2. **获取模板**：从code_templates.xml获取相应的代码模板
3. **查看实现细节**：从technical_details.xml获取具体实现要求

### B. 执行任务时
1. **严格按照架构设计**：参考architecture_design.xml中的目录结构
2. **使用标准模板**：基于code_templates.xml中的模板编写代码
3. **遵循技术决策**：按照technical_details.xml中记录的技术方案

### C. 完成任务后
1. **更新任务状态**：
```xml
<!-- 在task_tracker.xml中移动任务 -->
<!-- 从 <next_actions> 移动到 <completed_tasks> -->
<completed_tasks>
  <task id="X.X" title="任务标题" status="completed">
    <completion_notes>完成说明</completion_notes>
    <files_created>创建的文件列表</files_created>
    <files_modified>修改的文件列表</files_modified>
  </task>
</completed_tasks>
```

2. **更新当前进度**：
```xml
<!-- 更新当前任务和阶段 -->
<current_phase>phaseX</current_phase>
<current_task>X.X</current_task>
```

3. **设置下一步行动**：
```xml
<!-- 更新下一步行动 -->
<next_actions>
  <action priority="high">下一个任务的具体行动</action>
</next_actions>
```

## 关键原则

### 1. 任务执行原则
- **一次只做一个任务**：专注于task_tracker.xml中标记的当前任务
- **严格按照计划**：不要偏离implementation_plan.xml中的计划
- **保持一致性**：使用code_templates.xml中的标准模板

### 2. 文件操作原则
- **先查看再编辑**：使用view工具查看现有文件再进行修改
- **使用str-replace-editor**：不要重写整个文件，使用增量编辑
- **遵循目录结构**：严格按照architecture_design.xml中的目录结构

### 3. 记录更新原则
- **及时更新进度**：每完成一个子任务就更新task_tracker.xml
- **记录重要决策**：如果有新的技术决策，更新technical_details.xml
- **保持文档同步**：确保所有XML文件信息一致

## 紧急情况处理

### 如果发现任务阻塞
1. 在task_tracker.xml中记录：
```xml
<blocked_tasks>
  <task id="X.X" reason="阻塞原因" resolution="解决方案"/>
</blocked_tasks>
```

### 如果需要修改计划
1. 先在implementation_plan.xml中更新计划
2. 然后在task_tracker.xml中更新任务状态
3. 在technical_details.xml中记录变更原因

### 如果发现技术问题
1. 查看technical_details.xml中的相关实现方案
2. 如果需要调整，先更新technical_details.xml
3. 然后按新方案执行

## 状态检查清单

每次开始工作前检查：
- [ ] 已查看task_tracker.xml了解当前状态
- [ ] 已确认当前任务的依赖已完成
- [ ] 已获取相关的技术实现细节
- [ ] 已准备好相应的代码模板

每次完成工作后检查：
- [ ] 已更新task_tracker.xml中的任务状态
- [ ] 已设置下一步行动
- [ ] 已记录完成的文件和重要决策
- [ ] 已验证实现符合架构设计

## 重要提醒

1. **永远先查看.claude/tasks/目录下的所有XML文件**
2. **严格按照task_tracker.xml中的当前任务执行**
3. **每次完成任务都要更新进度记录**
4. **遇到问题时查看technical_details.xml寻找解决方案**
5. **保持项目的一致性和连续性**

## 快速恢复命令序列

```bash
# 1. 查看当前状态
view .claude/tasks/task_tracker.xml

# 2. 查看项目概览
view .claude/tasks/project_overview.xml

# 3. 查看当前阶段的实施计划
view .claude/tasks/implementation_plan.xml

# 4. 查看技术细节
view .claude/tasks/technical_details.xml

# 5. 开始执行当前任务
```

记住：你是在继续一个已经规划好的项目，不是重新开始。严格按照现有的计划和记录执行！
