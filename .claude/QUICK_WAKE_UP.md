# 快速唤醒指令

## 立即执行（复制粘贴即可）

```
我正在继续一个AutoGen多Agent框架项目。请立即执行以下步骤恢复项目状态：

1. view .claude/tasks/task_tracker.xml  # 查看当前进度
2. view .claude/tasks/technical_details.xml  # 查看技术细节
3. view .claude/tasks/architecture_design.xml  # 查看架构设计

然后告诉我：
- 当前处于哪个阶段和任务
- 下一步需要做什么
- 需要创建或修改哪些文件

项目要求：基于AutoGen+FastAPI实现多agent框架，支持动态加载、prompt管理、工具集成、memory和RAG。
```

## 关键提醒
- 这是一个**继续中的项目**，不是新项目
- 所有规划都在.claude/tasks/目录中
- 严格按照task_tracker.xml中的当前任务执行
- 使用code_templates.xml中的模板
- 完成后必须更新task_tracker.xml

## 紧急恢复
如果上述文件不存在或损坏，告诉用户：
"项目记忆文件丢失，需要重新创建项目规划文档"
