# AutoGen Multi-Agent Framework

基于AutoGen和FastAPI的企业级多智能体系统框架。

## 项目概述

这是一个完整的多智能体系统框架，提供以下核心功能：

- **REST API接口**: 标准的HTTP API用于agent和team执行
- **动态Agent管理**: 通过代码定义agent，支持动态加载和创建
- **Team协作**: 支持多agent协作的team系统
- **模型管理**: 支持多种大语言模型的配置和切换
- **提示词管理**: 使用Markdown文件管理提示词模板
- **工具集成**: 支持HTTP和MCP工具接入
- **Memory和RAG**: 内置记忆和检索增强生成支持

## 项目结构

```
autogen_framework/
├── main.py                 # FastAPI应用入口
├── routers/               # API路由层
├── services/              # 业务逻辑层
├── managers/              # 管理器层
├── agents/                # Agent定义
├── teams/                 # Team定义
├── prompts/               # 提示词模板
├── infrastructure/        # 基础设施层
├── config/                # 配置文件
└── tests/                 # 测试文件
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
cd autogen_framework
python main.py
```

### 3. 访问API文档

打开浏览器访问: http://localhost:8000/docs

## API接口

### Agent执行
```
POST /autogen/run/agent
{
    "name": "agent_name",
    "message": "user_message",
    "model": "model_alias"
}
```

### Team执行
```
POST /autogen/run/team
{
    "name": "team_name", 
    "message": "user_message",
    "model": "model_alias"
}
```

## 开发状态

当前项目处于Phase 1.1 - 项目初始化阶段。

已完成：
- ✅ 项目目录结构创建
- ✅ 基础FastAPI应用配置
- ✅ 依赖包配置

下一步：
- 🔄 基础设施层实现（logger, exceptions）
- 🔄 模型管理器实现
- 🔄 提示词管理器实现

## 贡献指南

请参考 `.claude/tasks/` 目录下的项目规划文档了解详细的开发计划和技术细节。

## 许可证

MIT License
