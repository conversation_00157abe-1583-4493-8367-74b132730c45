"""
代码审查Agent

专业的代码审查Agent，能够分析代码质量、发现潜在问题并提供改进建议。
"""

from typing import Dict, Any, Optional
from autogen import ConversableAgent

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from managers.model_manager import ModelManager
from managers.prompt_manager import get_prompt_manager


def create_code_reviewer_agent(
    model_alias: str,
    name: Optional[str] = None,
    **kwargs
) -> ConversableAgent:
    """
    创建代码审查Agent
    
    Args:
        model_alias: 模型别名
        name: Agent名称，默认为"CodeReviewer"
        **kwargs: 其他配置参数
    
    Returns:
        ConversableAgent实例
    """
    # 获取提示词
    prompt_manager = get_prompt_manager()
    system_prompt = prompt_manager.get_prompt("agents/code_reviewer", **kwargs)
    
    # 获取模型配置
    model_manager = ModelManager()
    model_config = model_manager.get_model_config(model_alias)
    
    # 创建Agent
    agent = ConversableAgent(
        name=name or "CodeReviewer",
        system_message=system_prompt,
        llm_config=model_config,
        human_input_mode="NEVER",  # 不需要人工输入
        max_consecutive_auto_reply=kwargs.get("max_consecutive_auto_reply", 10),
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        **{k: v for k, v in kwargs.items() if k not in [
            "max_consecutive_auto_reply", "name"
        ]}
    )
    
    return agent


def create_senior_code_reviewer_agent(
    model_alias: str,
    name: Optional[str] = None,
    **kwargs
) -> ConversableAgent:
    """
    创建高级代码审查Agent
    
    Args:
        model_alias: 模型别名
        name: Agent名称，默认为"SeniorCodeReviewer"
        **kwargs: 其他配置参数
    
    Returns:
        ConversableAgent实例
    """
    # 为高级审查员添加特殊指令
    special_instructions = """
    作为高级代码审查专家，你需要：
    1. 关注架构设计和系统性问题
    2. 提供性能优化和可扩展性建议
    3. 识别安全漏洞和最佳实践违规
    4. 给出具体的重构建议
    """
    
    kwargs["special_instructions"] = special_instructions
    
    # 获取提示词
    prompt_manager = get_prompt_manager()
    system_prompt = prompt_manager.get_prompt("agents/code_reviewer", **kwargs)
    
    # 获取模型配置
    model_manager = ModelManager()
    model_config = model_manager.get_model_config(model_alias)
    
    # 创建Agent
    agent = ConversableAgent(
        name=name or "SeniorCodeReviewer",
        system_message=system_prompt,
        llm_config=model_config,
        human_input_mode="NEVER",
        max_consecutive_auto_reply=kwargs.get("max_consecutive_auto_reply", 15),
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        **{k: v for k, v in kwargs.items() if k not in [
            "max_consecutive_auto_reply", "name", "special_instructions"
        ]}
    )
    
    return agent
