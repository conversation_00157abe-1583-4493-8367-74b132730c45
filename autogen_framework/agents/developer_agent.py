"""
开发者Agent

专业的软件开发Agent，能够编写代码、解决技术问题、实现功能需求。
"""

from typing import Dict, Any, Optional
from autogen import ConversableAgent

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from managers.model_manager import ModelManager
from managers.prompt_manager import get_prompt_manager


def create_developer_agent(
    model_alias: str,
    name: Optional[str] = None,
    programming_language: str = "Python",
    **kwargs
) -> ConversableAgent:
    """
    创建开发者Agent
    
    Args:
        model_alias: 模型别名
        name: Agent名称，默认为"Developer"
        programming_language: 主要编程语言
        **kwargs: 其他配置参数
    
    Returns:
        ConversableAgent实例
    """
    # 创建开发者提示词
    developer_prompt = f"""# 专业开发者

你是一位经验丰富的{programming_language}开发者，专门负责编写高质量的代码和解决技术问题。

## 专业技能

### 编程语言
- 主要语言：{programming_language}
- 熟练掌握相关框架和库
- 了解最佳实践和设计模式

### 开发能力
- **代码编写**: 编写清晰、高效、可维护的代码
- **问题解决**: 分析需求，设计解决方案
- **调试优化**: 发现并修复bug，优化性能
- **文档编写**: 提供清晰的代码注释和文档

## 工作原则

1. **代码质量**: 遵循编码规范，确保代码可读性
2. **功能完整**: 确保实现满足所有需求
3. **性能优化**: 关注代码效率和资源使用
4. **安全考虑**: 注意安全漏洞和最佳实践
5. **测试驱动**: 编写可测试的代码

## 输出格式

当编写代码时，请：
1. 提供完整的代码实现
2. 添加必要的注释说明
3. 说明设计思路和关键决策
4. 提供使用示例（如适用）

现在请开始你的开发工作。
"""
    
    # 获取模型配置
    model_manager = ModelManager()
    model_config = model_manager.get_model_config(model_alias)
    
    # 创建Agent
    agent = ConversableAgent(
        name=name or "Developer",
        system_message=developer_prompt,
        llm_config=model_config,
        human_input_mode="NEVER",
        max_consecutive_auto_reply=kwargs.get("max_consecutive_auto_reply", 10),
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        **{k: v for k, v in kwargs.items() if k not in [
            "max_consecutive_auto_reply", "name", "programming_language"
        ]}
    )
    
    return agent


def create_fullstack_developer_agent(
    model_alias: str,
    name: Optional[str] = None,
    **kwargs
) -> ConversableAgent:
    """
    创建全栈开发者Agent
    
    Args:
        model_alias: 模型别名
        name: Agent名称，默认为"FullstackDeveloper"
        **kwargs: 其他配置参数
    
    Returns:
        ConversableAgent实例
    """
    # 全栈开发者提示词
    fullstack_prompt = """# 全栈开发专家

你是一位经验丰富的全栈开发者，能够处理前端、后端、数据库等各个层面的开发任务。

## 技术栈

### 前端技术
- HTML5, CSS3, JavaScript/TypeScript
- React, Vue.js, Angular等现代框架
- 响应式设计和移动端适配

### 后端技术
- Python (Django, FastAPI, Flask)
- Node.js (Express, Nest.js)
- Java (Spring Boot)
- 微服务架构

### 数据库
- 关系型数据库 (MySQL, PostgreSQL)
- NoSQL数据库 (MongoDB, Redis)
- 数据库设计和优化

### DevOps
- Docker容器化
- CI/CD流水线
- 云服务部署

## 开发能力

1. **系统设计**: 设计可扩展的系统架构
2. **API开发**: 设计和实现RESTful API
3. **数据建模**: 设计高效的数据模型
4. **性能优化**: 前后端性能优化
5. **安全实践**: 实施安全最佳实践

## 工作流程

1. **需求分析**: 理解业务需求和技术要求
2. **架构设计**: 设计系统整体架构
3. **技术选型**: 选择合适的技术栈
4. **开发实现**: 编写高质量代码
5. **测试部署**: 确保系统稳定运行

现在请开始你的全栈开发工作。
"""
    
    # 获取模型配置
    model_manager = ModelManager()
    model_config = model_manager.get_model_config(model_alias)
    
    # 创建Agent
    agent = ConversableAgent(
        name=name or "FullstackDeveloper",
        system_message=fullstack_prompt,
        llm_config=model_config,
        human_input_mode="NEVER",
        max_consecutive_auto_reply=kwargs.get("max_consecutive_auto_reply", 12),
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        **{k: v for k, v in kwargs.items() if k not in [
            "max_consecutive_auto_reply", "name"
        ]}
    )
    
    return agent
