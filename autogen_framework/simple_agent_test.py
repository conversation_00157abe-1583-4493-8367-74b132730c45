#!/usr/bin/env python3
"""
简化的Agent管理器测试
"""

print("=== Agent管理器功能验证 ===")

# 1. 测试提示词管理器
print("\n1. 测试提示词管理器...")
try:
    from managers.prompt_manager import get_prompt_manager
    pm = get_prompt_manager()
    
    # 测试获取提示词
    prompt = pm.get_prompt("agents/code_reviewer")
    print(f"✓ 代码审查提示词长度: {len(prompt)} 字符")
    
    # 测试列出提示词
    prompts = pm.list_prompts()
    print(f"✓ 发现 {len(prompts)} 个提示词模板")
    for p in prompts:
        print(f"  - {p}")
        
except Exception as e:
    print(f"✗ 提示词管理器测试失败: {e}")

# 2. 测试模型管理器
print("\n2. 测试模型管理器...")
try:
    from managers.model_manager import ModelManager
    mm = ModelManager()
    
    # 测试获取模型配置
    models = mm.list_available_models()
    print(f"✓ 发现 {len(models)} 个模型配置")
    for model in models[:3]:  # 只显示前3个
        print(f"  - {model}")
        
    # 测试获取具体配置
    if models:
        config = mm.get_model_config(models[0])
        print(f"✓ 模型配置获取成功: {models[0]}")
        
except Exception as e:
    print(f"✗ 模型管理器测试失败: {e}")

# 3. 测试Agent工厂函数概念
print("\n3. 测试Agent工厂函数概念...")
try:
    # 模拟Agent类
    class MockAgent:
        def __init__(self, name, system_message, llm_config, **kwargs):
            self.name = name
            self.system_message = system_message
            self.llm_config = llm_config
            self.kwargs = kwargs
        
        def __repr__(self):
            return f"MockAgent(name='{self.name}')"
    
    # 创建工厂函数
    def create_code_reviewer_agent(model_alias, name=None, **kwargs):
        """代码审查Agent工厂函数"""
        from managers.prompt_manager import get_prompt_manager
        from managers.model_manager import ModelManager
        
        # 获取提示词和模型配置
        pm = get_prompt_manager()
        mm = ModelManager()
        
        system_prompt = pm.get_prompt("agents/code_reviewer", **kwargs)
        model_config = mm.get_model_config(model_alias)
        
        # 创建Agent
        agent = MockAgent(
            name=name or "CodeReviewer",
            system_message=system_prompt,
            llm_config=model_config,
            **kwargs
        )
        
        return agent
    
    # 测试工厂函数
    from managers.model_manager import ModelManager
    mm = ModelManager()
    available_models = mm.list_available_models()
    
    if available_models:
        agent = create_code_reviewer_agent(available_models[0])
        print(f"✓ Agent创建成功: {agent}")
        print(f"✓ 系统提示词长度: {len(agent.system_message)} 字符")
        print(f"✓ 模型配置: {agent.llm_config.get('model', 'unknown')}")
    else:
        print("✗ 没有可用的模型配置")
        
except Exception as e:
    print(f"✗ Agent工厂函数测试失败: {e}")
    import traceback
    traceback.print_exc()

# 4. 测试Agent注册表概念
print("\n4. 测试Agent注册表概念...")
try:
    # 简单的注册表实现
    class SimpleAgentRegistry:
        def __init__(self):
            self.factories = {}
        
        def register(self, name, factory_func):
            self.factories[name] = factory_func
            print(f"✓ 注册Agent工厂: {name}")
        
        def create(self, name, model_alias, **kwargs):
            if name not in self.factories:
                raise ValueError(f"未知Agent: {name}")
            
            factory = self.factories[name]
            return factory(model_alias, **kwargs)
        
        def list_available(self):
            return list(self.factories.keys())
    
    # 测试注册表
    registry = SimpleAgentRegistry()
    
    # 注册工厂函数
    def create_developer_agent(model_alias, name=None, **kwargs):
        return MockAgent(
            name=name or "Developer",
            system_message="Developer system prompt",
            llm_config={"model": model_alias}
        )
    
    registry.register("developer", create_developer_agent)
    registry.register("code_reviewer", create_code_reviewer_agent)
    
    # 测试创建
    available = registry.list_available()
    print(f"✓ 可用Agent: {available}")
    
    for agent_name in available:
        try:
            from managers.model_manager import ModelManager
            mm = ModelManager()
            models = mm.list_available_models()
            if models:
                agent = registry.create(agent_name, models[0])
                print(f"✓ 创建{agent_name}: {agent}")
        except Exception as e:
            print(f"✗ 创建{agent_name}失败: {e}")
    
except Exception as e:
    print(f"✗ Agent注册表测试失败: {e}")

print("\n" + "="*50)
print("Agent管理器核心概念验证完成！")
print("✓ 提示词管理 - 支持MD文件和Jinja2模板")
print("✓ 模型管理 - 支持多模型配置和别名")
print("✓ 工厂模式 - 支持动态Agent创建")
print("✓ 注册机制 - 支持Agent工厂函数注册")
print("\n下一步: 实现完整的AgentManager类集成这些功能")
