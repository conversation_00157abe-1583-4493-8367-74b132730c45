["tests/test_prompt_manager.py::TestPromptManager::test_cache_functionality", "tests/test_prompt_manager.py::TestPromptManager::test_error_handling", "tests/test_prompt_manager.py::TestPromptManager::test_get_metadata", "tests/test_prompt_manager.py::TestPromptManager::test_initialization", "tests/test_prompt_manager.py::TestPromptManager::test_list_prompts", "tests/test_prompt_manager.py::TestPromptManager::test_load_and_get_prompt", "tests/test_prompt_manager.py::TestPromptManager::test_parse_md_file_with_frontmatter", "tests/test_prompt_manager.py::TestPromptManager::test_parse_md_file_without_frontmatter", "tests/test_prompt_manager.py::TestPromptTemplate::test_basic_template", "tests/test_prompt_manager.py::TestPromptTemplate::test_complex_template", "tests/test_prompt_manager.py::TestPromptTemplate::test_template_with_metadata", "tests/test_prompt_manager.py::test_global_instance"]