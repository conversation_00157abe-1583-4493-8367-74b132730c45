#!/usr/bin/env python3
"""
测试更新后的Agent管理器和Agent定义
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_agent_manager():
    """测试Agent管理器基本功能"""
    print("=== 测试Agent管理器 ===")
    
    try:
        from managers.agent_manager import AgentManager
        
        # 创建Agent管理器
        agent_manager = AgentManager()
        print("✓ Agent管理器创建成功")
        
        # 测试列出可用Agent
        available = agent_manager.list_available_agents()
        print(f"✓ 可用Agent: {available}")
        
        if available:
            # 测试获取Agent信息
            for agent_name in available:
                try:
                    info = agent_manager.get_agent_info(agent_name)
                    print(f"✓ Agent信息 - {agent_name}: {info['name']}")
                except Exception as e:
                    print(f"✗ 获取{agent_name}信息失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """测试Agent创建"""
    print("\n=== 测试Agent创建 ===")
    
    try:
        from managers.agent_manager import AgentManager
        
        # 创建Agent管理器
        agent_manager = AgentManager()
        
        # 测试创建Agent（使用模拟模型）
        available = agent_manager.list_available_agents()
        
        if available:
            for agent_name in available:
                try:
                    # 使用模拟模型别名
                    agent = agent_manager.create_agent(agent_name, "mock-model")
                    print(f"✓ 创建{agent_name}成功: {agent.name}")
                except Exception as e:
                    print(f"✗ 创建{agent_name}失败: {e}")
        else:
            print("! 没有可用的Agent工厂函数")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_manager():
    """测试提示词管理器"""
    print("\n=== 测试提示词管理器 ===")
    
    try:
        from managers.prompt_manager import get_prompt_manager
        
        pm = get_prompt_manager()
        print("✓ 提示词管理器创建成功")
        
        # 测试列出提示词
        prompts = pm.list_prompts()
        print(f"✓ 发现 {len(prompts)} 个提示词模板")
        
        for prompt in prompts:
            print(f"  - {prompt}")
        
        # 测试获取提示词
        if prompts:
            try:
                content = pm.get_prompt(prompts[0])
                print(f"✓ 获取提示词成功: {prompts[0]} ({len(content)} 字符)")
            except Exception as e:
                print(f"✗ 获取提示词失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_manager():
    """测试模型管理器"""
    print("\n=== 测试模型管理器 ===")
    
    try:
        from managers.model_manager import ModelManager
        
        mm = ModelManager()
        print("✓ 模型管理器创建成功")
        
        # 测试列出模型
        models = mm.list_models()
        print(f"✓ 发现 {len(models)} 个模型配置")
        
        for model in models[:3]:  # 只显示前3个
            print(f"  - {model}")
        
        # 测试获取模型信息
        if models:
            try:
                info = mm.get_model_info(models[0])
                print(f"✓ 获取模型信息成功: {models[0]}")
            except Exception as e:
                print(f"✗ 获取模型信息失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    print("\n=== 测试集成功能 ===")
    
    try:
        from managers.agent_manager import AgentManager
        from managers.model_manager import ModelManager
        from managers.prompt_manager import get_prompt_manager
        
        # 创建管理器
        agent_manager = AgentManager()
        model_manager = ModelManager()
        prompt_manager = get_prompt_manager()
        
        print("✓ 所有管理器创建成功")
        
        # 测试完整流程
        available_agents = agent_manager.list_available_agents()
        available_models = model_manager.list_models()
        available_prompts = prompt_manager.list_prompts()
        
        print(f"✓ 系统状态:")
        print(f"  - Agent工厂: {len(available_agents)} 个")
        print(f"  - 模型配置: {len(available_models)} 个")
        print(f"  - 提示词模板: {len(available_prompts)} 个")
        
        # 如果都有可用资源，测试创建Agent
        if available_agents and available_models:
            try:
                agent = agent_manager.create_agent(
                    available_agents[0], 
                    available_models[0]
                )
                print(f"✓ 集成测试成功: 创建了 {agent.name}")
            except Exception as e:
                print(f"! 集成测试警告: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始AutoGen 0.6.2 Agent管理器测试...\n")
    
    success = True
    success &= test_prompt_manager()
    success &= test_model_manager()
    success &= test_agent_manager()
    success &= test_agent_creation()
    success &= test_integration()
    
    print(f"\n{'='*50}")
    if success:
        print("✓ 所有测试通过！")
        print("\n下一步:")
        print("1. 安装AutoGen 0.6.2依赖")
        print("2. 配置模型客户端")
        print("3. 测试真实的Agent创建和运行")
        return 0
    else:
        print("✗ 部分测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
