"""
Agent管理器

负责Agent工厂函数的注册、管理和动态创建Agent实例。
支持从agents目录自动发现和注册Agent工厂函数。
使用AutoGen 0.6.2的新API。
"""

import os
import importlib
import importlib.util
import inspect
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable

try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    # 如果AutoGen不可用，使用模拟类
    AssistantAgent = None
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

from .model_manager import ModelManager
from .prompt_manager import get_prompt_manager

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.agent_manager")


class AgentManager:
    """Agent管理器"""
    
    def __init__(self, agents_dir: Optional[str] = None, model_manager: Optional[ModelManager] = None):
        """
        初始化Agent管理器
        
        Args:
            agents_dir: Agent定义文件目录，默认为 autogen_framework/agents
            model_manager: 模型管理器实例
        """
        if agents_dir is None:
            # 获取当前文件所在目录的父目录下的agents目录
            current_dir = Path(__file__).parent.parent
            agents_dir = current_dir / "agents"
        
        self.agents_dir = Path(agents_dir)
        self.model_manager = model_manager or ModelManager()
        self.prompt_manager = get_prompt_manager()
        
        # Agent工厂函数注册表
        self._factories: Dict[str, Callable] = {}
        
        # Agent实例缓存
        self._cache: Dict[str, Any] = {}
        
        # 确保agents目录存在
        self.agents_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"AgentManager initialized with directory: {self.agents_dir}")
        
        # 自动发现和注册Agent工厂函数
        self._auto_discover_agents()
    
    def _auto_discover_agents(self) -> None:
        """自动发现agents目录中的Agent工厂函数并注册"""
        if not self.agents_dir.exists():
            logger.warning(f"Agents directory not found: {self.agents_dir}")
            return
        
        # 扫描agents目录中的Python文件
        for py_file in self.agents_dir.glob("*_agent.py"):
            try:
                self._load_agent_module(py_file)
            except Exception as e:
                logger.error(f"Failed to load agent module {py_file}: {str(e)}")
    
    def _load_agent_module(self, py_file: Path) -> None:
        """
        加载Agent模块并注册工厂函数
        
        Args:
            py_file: Python文件路径
        """
        # 构建模块名
        module_name = py_file.stem  # 去掉.py扩展名
        
        # 动态导入模块
        spec = importlib.util.spec_from_file_location(module_name, py_file)
        if spec is None or spec.loader is None:
            logger.error(f"Cannot load module spec for {py_file}")
            return
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 查找create_*_agent函数
        for name, obj in inspect.getmembers(module):
            if (inspect.isfunction(obj) and 
                name.startswith("create_") and 
                name.endswith("_agent")):
                
                # 提取agent名称（去掉create_和_agent）
                agent_name = name[7:-6]  # create_xxx_agent -> xxx
                
                # 注册工厂函数
                self.register_agent_factory(agent_name, obj)
                logger.info(f"Auto-registered agent factory: {agent_name} from {py_file}")
    
    def register_agent_factory(self, name: str, factory_func: Callable) -> None:
        """
        注册Agent工厂函数
        
        Args:
            name: Agent名称
            factory_func: 工厂函数，应该接受model_alias和**kwargs参数
        """
        if not callable(factory_func):
            raise ValueError(f"Factory function must be callable: {factory_func}")
        
        # 验证函数签名
        sig = inspect.signature(factory_func)
        if 'model_alias' not in sig.parameters:
            logger.warning(f"Factory function {name} should accept 'model_alias' parameter")
        
        self._factories[name] = factory_func
        logger.info(f"Registered agent factory: {name}")
    
    def create_agent(self, name: str, model_alias: str, **kwargs) -> Any:
        """
        创建Agent实例
        
        Args:
            name: Agent名称
            model_alias: 模型别名
            **kwargs: 其他参数
            
        Returns:
            ConversableAgent实例
        """
        if name not in self._factories:
            available = list(self._factories.keys())
            raise ValueError(f"Unknown agent: {name}. Available agents: {available}")
        
        # 生成缓存键
        cache_key = f"{name}_{model_alias}_{hash(str(sorted(kwargs.items())))}"
        
        # 检查缓存
        if cache_key in self._cache and not kwargs.get('force_create', False):
            logger.debug(f"Returning cached agent: {cache_key}")
            return self._cache[cache_key]
        
        try:
            # 调用工厂函数创建Agent
            factory_func = self._factories[name]
            agent = factory_func(model_alias=model_alias, **kwargs)
            
            if AUTOGEN_AVAILABLE and AssistantAgent and not isinstance(agent, AssistantAgent):
                logger.warning(f"Factory function returned {type(agent)}, expected AssistantAgent")
            elif not AUTOGEN_AVAILABLE:
                logger.debug(f"AutoGen not available, skipping type check for {type(agent)}")
            
            # 缓存Agent实例
            self._cache[cache_key] = agent
            
            logger.info(f"Created agent: {name} with model: {model_alias}")
            return agent
            
        except Exception as e:
            logger.error(f"Failed to create agent {name}: {str(e)}")
            raise ValueError(f"Failed to create agent {name}: {str(e)}")
    
    def list_available_agents(self) -> List[str]:
        """
        列出所有可用的Agent
        
        Returns:
            Agent名称列表
        """
        return list(self._factories.keys())
    
    def get_agent_info(self, name: str) -> Dict[str, Any]:
        """
        获取Agent信息
        
        Args:
            name: Agent名称
            
        Returns:
            Agent信息字典
        """
        if name not in self._factories:
            raise ValueError(f"Unknown agent: {name}")
        
        factory_func = self._factories[name]
        sig = inspect.signature(factory_func)
        
        return {
            "name": name,
            "factory_function": factory_func.__name__,
            "module": factory_func.__module__,
            "signature": str(sig),
            "docstring": factory_func.__doc__
        }
    
    def clear_cache(self) -> None:
        """清空Agent缓存"""
        self._cache.clear()
        logger.info("Agent cache cleared")
    
    def reload_agents(self) -> None:
        """重新加载所有Agent定义"""
        # 清空注册表和缓存
        self._factories.clear()
        self._cache.clear()
        
        # 重新发现和注册
        self._auto_discover_agents()
        
        logger.info("All agents reloaded")


# 全局实例
_default_manager = None


def get_agent_manager() -> AgentManager:
    """获取默认的Agent管理器实例"""
    global _default_manager
    if _default_manager is None:
        _default_manager = AgentManager()
    return _default_manager
